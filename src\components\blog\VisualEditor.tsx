"use client";

import React, { useRef, useEffect, useState } from 'react';

interface VisualEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const VisualEditor: React.FC<VisualEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  className = ""
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Convert markdown to HTML for display
  const markdownToHtml = (markdown: string): string => {
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mb-2 mt-4 outline-none" contenteditable="true" data-type="heading" data-level="3">$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mb-3 mt-5 outline-none" contenteditable="true" data-type="heading" data-level="2">$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 mt-6 outline-none" contenteditable="true" data-type="heading" data-level="1">$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold" contenteditable="true" data-type="bold">$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em class="italic" contenteditable="true" data-type="italic">$1</em>');

    // Code
    html = html.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" contenteditable="true" data-type="code">$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" contenteditable="true" data-type="link" data-url="$2">$1</a>');

    // Images
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4" data-type="image" data-alt="$1" />');

    // Lists
    html = html.replace(/^\- (.*$)/gim, '<li class="ml-4 outline-none" contenteditable="true" data-type="list-item">• $1</li>');
    html = html.replace(/(<li.*<\/li>)/s, '<ul class="mb-4" data-type="list">$1</ul>');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4 outline-none" contenteditable="true" data-type="blockquote">$1</blockquote>');

    // Line breaks and paragraphs
    html = html.replace(/\n\n/g, '</p><p class="mb-4 outline-none" contenteditable="true" data-type="paragraph">');
    html = html.replace(/\n/g, '<br />');

    // Wrap in paragraphs if not already wrapped
    if (html && !html.startsWith('<')) {
      html = '<p class="mb-4 outline-none" contenteditable="true" data-type="paragraph">' + html + '</p>';
    }

    return html;
  };

  // Convert HTML back to markdown
  const htmlToMarkdown = (html: string): string => {
    let markdown = html;

    // Headers
    markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1');
    markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1');
    markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1');

    // Bold
    markdown = markdown.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**');

    // Italic
    markdown = markdown.replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*');

    // Code
    markdown = markdown.replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`');

    // Links
    markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)');

    // Images
    markdown = markdown.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*\/?>/gi, '![$2]($1)');

    // Lists
    markdown = markdown.replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1');
    markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/gi, '$1');

    // Blockquotes
    markdown = markdown.replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1');

    // Paragraphs
    markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n');

    // Clean up extra line breaks
    markdown = markdown.replace(/\n\n+/g, '\n\n');
    markdown = markdown.replace(/<br\s*\/?>/gi, '\n');

    // Remove HTML tags
    markdown = markdown.replace(/<[^>]*>/g, '');

    // Clean up
    markdown = markdown.trim();

    return markdown;
  };

  // Update editor content when value changes
  useEffect(() => {
    if (editorRef.current && !isUpdating) {
      const html = markdownToHtml(value);
      editorRef.current.innerHTML = html;
    }
  }, [value, isUpdating]);

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current) {
      setIsUpdating(true);
      const html = editorRef.current.innerHTML;
      const markdown = htmlToMarkdown(html);
      onChange(markdown);
      setTimeout(() => setIsUpdating(false), 100);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          document.execCommand('bold');
          handleInput();
          break;
        case 'i':
          e.preventDefault();
          document.execCommand('italic');
          handleInput();
          break;
        case 'u':
          e.preventDefault();
          document.execCommand('underline');
          handleInput();
          break;
        case 'k':
          e.preventDefault();
          const selection = window.getSelection();
          if (selection && selection.toString()) {
            const url = prompt('Enter URL:');
            if (url) {
              document.execCommand('createLink', false, url);
              handleInput();
            }
          }
          break;
      }
    }

    // Handle Enter key for better formatting
    if (e.key === 'Enter') {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;
        const element = container.nodeType === Node.TEXT_NODE ? container.parentElement : container as Element;

        // If we're in a heading, create a new paragraph
        if (element && element.tagName && element.tagName.match(/^H[1-6]$/)) {
          e.preventDefault();
          const p = document.createElement('p');
          p.className = 'mb-4 outline-none';
          p.setAttribute('contenteditable', 'true');
          p.setAttribute('data-type', 'paragraph');
          p.innerHTML = '<br>';
          element.parentNode?.insertBefore(p, element.nextSibling);

          // Move cursor to new paragraph
          const newRange = document.createRange();
          newRange.setStart(p, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          handleInput();
        }
      }
    }
  };

  // Handle paste events
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    const html = e.clipboardData.getData('text/html');

    // If HTML is available and contains formatting, use it
    if (html && html.includes('<')) {
      // Clean up the HTML and insert
      const cleanHtml = html
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/<style[^>]*>.*?<\/style>/gi, '')
        .replace(/on\w+="[^"]*"/gi, '');

      document.execCommand('insertHTML', false, cleanHtml);
    } else {
      // Insert as plain text
      document.execCommand('insertText', false, text);
    }
    handleInput();
  };

  // Handle click events for interactive elements
  const handleClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;

    // Handle link clicks in edit mode
    if (target.tagName === 'A') {
      e.preventDefault();
      const currentUrl = target.getAttribute('href') || '';
      const currentText = target.textContent || '';

      const newUrl = prompt('Edit URL:', currentUrl);
      if (newUrl !== null) {
        if (newUrl === '') {
          // Remove link
          const textNode = document.createTextNode(currentText);
          target.parentNode?.replaceChild(textNode, target);
        } else {
          // Update link
          target.setAttribute('href', newUrl);
        }
        handleInput();
      }
    }

    // Handle image clicks
    if (target.tagName === 'IMG') {
      e.preventDefault();
      const currentSrc = target.getAttribute('src') || '';
      const currentAlt = target.getAttribute('alt') || '';

      const newSrc = prompt('Edit image URL:', currentSrc);
      if (newSrc !== null) {
        if (newSrc === '') {
          // Remove image
          target.remove();
        } else {
          // Update image
          target.setAttribute('src', newSrc);
          const newAlt = prompt('Edit alt text:', currentAlt);
          if (newAlt !== null) {
            target.setAttribute('alt', newAlt);
          }
        }
        handleInput();
      }
    }
  };

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        onClick={handleClick}
        className="w-full px-4 py-3 min-h-[400px] bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 prose prose-sm max-w-none"
        style={{ whiteSpace: 'pre-wrap' }}
        suppressContentEditableWarning={true}
        data-placeholder={placeholder}
        spellCheck={true}
      />
      
      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
          font-style: italic;
        }

        [contenteditable] h1:focus,
        [contenteditable] h2:focus,
        [contenteditable] h3:focus,
        [contenteditable] p:focus,
        [contenteditable] blockquote:focus,
        [contenteditable] li:focus {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
          border-radius: 4px;
          background-color: rgba(59, 130, 246, 0.05);
        }

        [contenteditable] a {
          cursor: pointer;
          position: relative;
          text-decoration: underline;
          text-decoration-color: #3b82f6;
        }

        [contenteditable] a:hover {
          background-color: rgba(59, 130, 246, 0.1);
          border-radius: 2px;
        }

        [contenteditable] a:hover:after {
          content: "Click to edit link";
          position: absolute;
          bottom: -25px;
          left: 0;
          background: #1f2937;
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 11px;
          white-space: nowrap;
          z-index: 10;
        }

        [contenteditable] img {
          cursor: pointer;
          border: 2px solid transparent;
          border-radius: 8px;
          transition: all 0.2s ease;
        }

        [contenteditable] img:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
          transform: scale(1.02);
        }

        [contenteditable] code {
          background-color: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        [contenteditable] blockquote {
          border-left: 4px solid #3b82f6;
          background-color: rgba(59, 130, 246, 0.05);
          border-radius: 0 4px 4px 0;
        }

        [contenteditable] ul {
          list-style: none;
        }

        [contenteditable] li {
          position: relative;
          padding-left: 20px;
        }

        [contenteditable] li:before {
          content: "•";
          color: #3b82f6;
          font-weight: bold;
          position: absolute;
          left: 0;
        }

        /* Selection styling */
        [contenteditable] ::selection {
          background-color: rgba(59, 130, 246, 0.2);
        }

        /* Focus ring for the entire editor */
        [contenteditable]:focus {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      `}</style>
    </div>
  );
};

export default VisualEditor;
